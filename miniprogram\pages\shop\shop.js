// pages/shop/shop.js
const { productApi, cartApi, postApi } = require('../../utils/api');

Page({
  data: {
    categories: [],
    subCategories: [],
    products: [],
    banners: [],
    currentCategory: 0,
    currentSubCategory: 0,
    loading: true,
    refreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    sortType: 'default', // default, sales, price-asc, price-desc
    showFilter: false,
    filterOptions: {
      minPrice: '',
      maxPrice: '',
      onlyDiscount: false,
      onlyNew: false
    },
    // 搜索相关
    searchKeyword: '',
    // 导航栏高度
    statusBarHeight: 0,
    navBarHeight: 44,
    // 搜索框宽度
    searchWidth: 0,
    // 胶囊按钮信息
    menuButtonInfo: null,
    // 窗口宽度
    windowWidth: 0,
    // 购物车数量
    cartCount: 0
  },

  onLoad: function (options) {
    // 商城页面加载

    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();

    // 计算胶囊按钮的位置信息
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

    // 计算搜索框的宽度为屏幕宽度的60%
    const searchWidth = systemInfo.windowWidth * 0.6;

    // 设置状态栏高度的 CSS 变量
    wx.setStorageSync('statusBarHeight', systemInfo.statusBarHeight);

    // 设置默认轮播图数据，确保页面始终有轮播图显示
    const defaultBanners = [
      {
        id: 1,
        imageUrl: '/images/lunbo/001.jpeg',
        type: 'page',
        url: '/pages/product/list?category=1',
        title: '企业服务专场'
      },
      {
        id: 2,
        imageUrl: '/images/lunbo/002.jpg',
        type: 'page',
        url: '/pages/product/list?category=2',
        title: '知识产权服务'
      },
      {
        id: 3,
        imageUrl: '/images/lunbo/003.png',
        type: 'page',
        url: '/pages/product/list?category=3',
        title: '财税服务专区'
      },
      {
        id: 4,
        imageUrl: '/images/lunbo/004.webp',
        type: 'page',
        url: '/pages/product/list?category=4',
        title: '工商注册服务'
      }
    ];

    this.setData({
      statusBarHeight: systemInfo.statusBarHeight,
      searchWidth: searchWidth,
      menuButtonInfo: menuButtonInfo,
      windowWidth: systemInfo.windowWidth,
      banners: defaultBanners // 直接设置默认轮播图数据
    });

    // 已设置默认轮播图数据

    // 尝试从API获取轮播图数据
    this.getBanners();

    // 获取分类数据（商品加载顺序优化，分类数据获取后再加载商品）
    this.getCategories(options);

    // 获取购物车数量
    this.getCartCount();

    // 启动定时器，定期检查购物车更新
    this.startCartUpdateChecker();

    // 如果有传入的分类参数，切换到对应分类
    if (options.categoryId) {
      const categoryId = parseInt(options.categoryId);
      // 查找分类索引
      const categoryIndex = this.data.categories.findIndex(cat => cat.id === categoryId);
      if (categoryIndex >= 0) {
        this.setData({ currentCategory: categoryIndex });
        this.getSubCategories(categoryId, options);
      }
    }

    // 如果有传入的子分类参数，切换到对应子分类
    if (options.subCategoryId) {
      const subCategoryId = parseInt(options.subCategoryId);
      // 等待子分类加载完成后再切换
      setTimeout(() => {
        const subCategoryIndex = this.data.subCategories.findIndex(subCat => subCat.id === subCategoryId);
        if (subCategoryIndex >= 0) {
          this.setData({ currentSubCategory: subCategoryIndex });
          this.getProducts();
        }
      }, 500);
    }
  },

  // 启动购物车更新检查器
  startCartUpdateChecker: function() {
    // 每3秒检查一次购物车更新
    this.cartUpdateTimer = setInterval(() => {
      this.checkCartUpdated();
    }, 3000);
  },

  // 页面卸载时清除定时器
  onUnload: function() {
    if (this.cartUpdateTimer) {
      clearInterval(this.cartUpdateTimer);
    }
  },

  // 页面显示时更新购物车数量
  onShow: function() {
    // 每次页面显示时都刷新购物车数量
    this.getCartCount();
    // 检查购物车是否有更新
    this.checkCartUpdated && this.checkCartUpdated();
  },

  // 检查购物车是否有更新
  checkCartUpdated: function() {
    // 获取上次购物车更新时间
    const lastUpdated = wx.getStorageSync('cartItemsUpdated');
    const lastChecked = this.lastCheckedTime || 0;

    // 如果购物车有更新，则更新购物车数量
    if (lastUpdated && lastUpdated > lastChecked) {
      // 购物车数据已更新
      this.updateCartCount();
      this.lastCheckedTime = lastUpdated;

      // 购物车已更新，静默更新徽标
      console.log('购物车已更新，徽标已刷新');
    }
  },

  // 获取分类数据
  getCategories: function(options) {
    // 开始获取分类数据
    const localCategories = [
      { id: 1, name: '企业服务' },
      { id: 2, name: '平台使用' },
      { id: 3, name: '合伙人' },
      { id: 4, name: '周边产品' }
    ];
    // 先设置本地数据，确保界面有内容显示
    this.setData({
      categories: localCategories,
      currentCategory: 0,
      currentSubCategory: 0
    });
    // 获取第一个分类的子分类
    if (localCategories.length > 0) {
      this.getSubCategories(localCategories[0].id, options);
    }
    // 使用 API 获取分类数据
    productApi.getShopCategories()
      .then(res => {
        // 获取分类数据成功
        if (res.success) {
          const categories = res.data;
          this.setData({
            categories: categories,
            currentCategory: 0,
            currentSubCategory: 0
          });
          // 获取第一个分类的子分类
          if (categories.length > 0) {
            this.getSubCategories(categories[0].id, options);
          }
        } else {
          // 获取分类数据失败
          wx.showToast({
            title: '获取分类失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        // 获取分类数据出错
      });
  },

  // 获取子分类数据
  getSubCategories: function(categoryId, options) {
    // 开始获取子分类数据
    const subCategoriesMap = {
      1: [
        { id: 101, name: '工商注册' },
        { id: 102, name: '财税服务' },
        { id: 103, name: '知识产权' },
        { id: 104, name: '法律服务' }
      ],
      2: [
        { id: 201, name: '会员服务' },
        { id: 202, name: '平台指南' },
        { id: 203, name: '增值服务' },
        { id: 204, name: '积分套餐' }
      ],
      3: [
        { id: 301, name: '城市合伙人' },
        { id: 302, name: '服务商' },
        { id: 303, name: '推广员' }
      ],
      4: [
        { id: 401, name: '办公用品' },
        { id: 402, name: '数码产品' },
        { id: 403, name: '礼品定制' }
      ]
    };
    const localSubCategories = subCategoriesMap[categoryId] || [];
    this.setData({
      subCategories: localSubCategories,
      currentSubCategory: 0
    });
    productApi.getShopSubCategories(categoryId)
      .then(res => {
        // 获取子分类数据成功
        if (res.success) {
          const subCategories = res.data;
          if (Array.isArray(subCategories) && subCategories.length > 0) {
            this.setData({
              subCategories: subCategories,
              currentSubCategory: 0
            });
          }
          // 处理页面参数跳转
          if (options && options.subCategoryId) {
            const subCategoryId = parseInt(options.subCategoryId);
            const subCategoryIndex = this.data.subCategories.findIndex(subCat => subCat.id === subCategoryId);
            if (subCategoryIndex >= 0) {
              this.setData({ currentSubCategory: subCategoryIndex });
            }
          }
          this.getProducts();
        } else {
          // 获取子分类数据失败
          this.getProducts();
        }
      })
      .catch(err => {
        // 获取子分类数据出错
        this.getProducts();
      });
  },

  // 获取商品数据
  getProducts: function(append = false) {
    // 开始获取商品数据

    const { currentCategory, currentSubCategory, page, pageSize, sortType, filterOptions, searchKeyword } = this.data;
    const categoryId = this.data.categories[currentCategory]?.id || '';
    const subCategoryId = this.data.subCategories[currentSubCategory]?.id || '';

    // 当前分类信息准备完成

    // 设置加载状态
    if (!append) {
      this.setData({
        loading: true
      });
    }

    // 构建API请求参数
    const params = {
      page: page,
      pageSize: pageSize,
      sortType: sortType,
      keyword: searchKeyword
    };

    if (categoryId) {
      params.categoryId = categoryId;
    }

    if (subCategoryId) {
      params.subCategoryId = subCategoryId;
    }

    // 请求参数准备完成

    if (filterOptions) {
      if (filterOptions.minPrice) {
        params.minPrice = filterOptions.minPrice;
      }

      if (filterOptions.maxPrice) {
        params.maxPrice = filterOptions.maxPrice;
      }

      if (filterOptions.onlyDiscount) {
        params.onlyDiscount = filterOptions.onlyDiscount;
      }

      if (filterOptions.onlyNew) {
        params.onlyNew = filterOptions.onlyNew;
      }
    }

    // 添加调试信息
    // 准备发送API请求获取商品数据

    // 使用API获取商品数据
    productApi.getProducts(params)
      .then(res => {
        // 获取商品数据成功
        // 响应数据处理

        if (res.success) {
          // 检查返回的数据结构，兼容两种格式
          let products = [];

          if (Array.isArray(res.data)) {
            products = res.data;
            console.log('数据是数组格式');
          } else if (res.data && res.data.list && Array.isArray(res.data.list)) {
            products = res.data.list;
            console.log('数据是对象格式，包含list数组');
          } else if (res.data) {
            console.log('数据格式不符合预期，尝试解析:', typeof res.data);
            // 尝试其他可能的格式
            if (typeof res.data === 'string') {
              try {
                const parsedData = JSON.parse(res.data);
                if (Array.isArray(parsedData)) {
                  products = parsedData;
                  console.log('成功从字符串解析为数组');
                } else if (parsedData.list && Array.isArray(parsedData.list)) {
                  products = parsedData.list;
                  console.log('成功从字符串解析为对象，包含list数组');
                }
              } catch (e) {
                console.error('解析响应数据失败:', e);
              }
            }
          }

          console.log('获取到的商品数量:', products.length);

          if (products.length === 0) {
            console.log('没有获取到商品数据，检查响应:', res);
          }

          // 检查是否有会员服务产品
          const memberProducts = Array.isArray(products) ? products.filter(p => p.subCategoryId === 201) : [];
          console.log('会员服务产品数量:', memberProducts.length);
          console.log('会员服务产品:', memberProducts);

          // 处理商品数据，将 images 字符串转换为数组
          products = products.map(product => {
            console.log('处理商品数据:', product);

            // 如果 images 是字符串，尝试解析为 JSON
            if (product.images && typeof product.images === 'string') {
              try {
                product.images = JSON.parse(product.images);
                console.log('成功解析商品图片:', product.images);
              } catch (e) {
                console.error('解析商品图片失败:', e);
                // 解析失败时，尝试使用简单的字符串分割
                if (product.images.includes(',')) {
                  product.images = product.images.split(',');
                  console.log('使用逗号分割解析图片:', product.images);
                } else {
                  // 如果不包含逗号，可能是单个图片路径，直接放入数组
                  product.images = [product.images];
                  console.log('使用单个图片路径:', product.images);
                }
              }
            } else if (!product.images || !Array.isArray(product.images)) {
              // 如果没有图片或者不是数组，设置默认图片
              product.images = ['/images/xinxi/企业服务.jpg'];
              console.log('设置默认图片:', product.images);
            }

            // 确保 id 字段存在（兼容前端代码）
            if (!product.id && product._id) {
              product.id = product._id;
            } else if (!product.id) {
              // 如果既没有id也没有_id，生成一个临时id
              product.id = 'product_' + Date.now() + '_' + Math.floor(Math.random() * 1000);
            }

            // 确保价格字段是字符串
            if (product.price && typeof product.price !== 'string') {
              product.price = product.price.toString();
            }

            if (product.originalPrice && typeof product.originalPrice !== 'string') {
              product.originalPrice = product.originalPrice.toString();
            }

            return product;
          });

          // 更新数据
          this.setData({
            products: append ? this.data.products.concat(products) : products,
            loading: false,
            refreshing: false,
            hasMore: products.length === pageSize // 如果返回的数据量等于页大小，则认为有更多数据
          });
        } else {
          console.warn('获取商品数据失败', res);

          // 显示错误提示
          wx.showToast({
            title: res.message || '获取商品数据失败',
            icon: 'none'
          });

          this.setData({
            loading: false,
            refreshing: false
          });
        }
      })
      .catch(err => {
        console.error('获取商品数据出错', err);
        console.error('错误详情:', JSON.stringify(err));

        // 显示错误提示
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none',
          duration: 3000
        });

        this.setData({
          loading: false,
          refreshing: false
        });
      });
  },

  // 切换一级分类
  switchCategory: function(e) {
    const index = e.currentTarget.dataset.index;
    if (index === this.data.currentCategory) {
      return;
    }

    this.setData({
      currentCategory: index,
      currentSubCategory: 0,
      loading: true,
      page: 1,
      products: []
    });

    // 获取子分类
    const categoryId = this.data.categories[index].id;
    this.getSubCategories(categoryId, this.data.options);
  },

  // 切换二级分类
  switchSubCategory: function(e) {
    const index = e.currentTarget.dataset.index;
    if (index === this.data.currentSubCategory) {
      return;
    }

    this.setData({
      currentSubCategory: index,
      loading: true,
      page: 1,
      products: []
    });

    // 获取商品
    this.getProducts();
  },

  // 切换排序方式
  switchSortType: function(e) {
    const sortType = e.currentTarget.dataset.type;
    if (sortType === this.data.sortType) {
      return;
    }

    this.setData({
      sortType: sortType,
      loading: true,
      page: 1,
      products: []
    });

    // 获取商品
    this.getProducts();
  },

  // 显示筛选面板
  showFilter: function() {
    this.setData({
      showFilter: true
    });
  },

  // 隐藏筛选面板
  hideFilter: function() {
    this.setData({
      showFilter: false
    });
  },

  // 输入最低价格
  inputMinPrice: function(e) {
    const filterOptions = this.data.filterOptions;
    filterOptions.minPrice = e.detail.value;

    this.setData({
      filterOptions: filterOptions
    });
  },

  // 输入最高价格
  inputMaxPrice: function(e) {
    const filterOptions = this.data.filterOptions;
    filterOptions.maxPrice = e.detail.value;

    this.setData({
      filterOptions: filterOptions
    });
  },

  // 切换仅显示优惠商品
  toggleOnlyDiscount: function() {
    const filterOptions = this.data.filterOptions;
    filterOptions.onlyDiscount = !filterOptions.onlyDiscount;

    this.setData({
      filterOptions: filterOptions
    });
  },

  // 切换仅显示新品
  toggleOnlyNew: function() {
    const filterOptions = this.data.filterOptions;
    filterOptions.onlyNew = !filterOptions.onlyNew;

    this.setData({
      filterOptions: filterOptions
    });
  },

  // 重置筛选条件
  resetFilter: function() {
    this.setData({
      filterOptions: {
        minPrice: '',
        maxPrice: '',
        onlyDiscount: false,
        onlyNew: false
      }
    });
  },

  // 应用筛选条件
  applyFilter: function() {
    this.setData({
      showFilter: false,
      loading: true,
      page: 1,
      products: []
    });

    // 获取商品
    this.getProducts();
  },

  // 点击商品
  onProductTap: function(e) {
    const id = e.currentTarget.dataset.id;
    const product = this.data.products.find(p => p.id === id || p._id === id);
    console.log('点击商品，详细信息:', {
      clickedId: id,
      idType: typeof id,
      product: product ? {
        id: product.id,
        _id: product._id,
        name: product.name
      } : null
    });
    wx.navigateTo({
      url: `/pages/product/detail?id=${id}`
    });
  },

  // 加入购物车（需要登录）
  addToCart: function(e) {
    e.stopPropagation && e.stopPropagation();
    wx.vibrateShort({ type: 'light' });
    const app = getApp();
    const id = e.currentTarget.dataset.id;
    app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.performAddToCart(id);
      }
    });
  },

  // 执行添加到购物车操作
  performAddToCart: function(id) {
    // 显示加载中
    wx.showLoading({
      title: '正在添加到购物车',
      mask: true
    });

    // 使用API添加商品到购物车
    cartApi.addToCart(id, 1)
      .then(res => {
        wx.hideLoading();

        if (res.success) {
          // 显示成功提示
          wx.showToast({
            title: '已加入购物车',
            icon: 'success',
            duration: 1500
          });

          // 更新购物车数量
          this.getCartCount();
        } else {
          wx.showToast({
            title: res.message || '添加失败',
            icon: 'none',
            duration: 2000
          });
        }
      })
      .catch(err => {
        console.error('添加到购物车失败', err);
        wx.hideLoading();

        // 显示错误提示
        wx.showToast({
          title: err.message || '网络错误，请稍后再试',
          icon: 'none',
          duration: 2000
        });
      });
  },

  // 加入购物车并跳转（需要登录）
  addToCartAndView: function(e) {
    e.stopPropagation && e.stopPropagation();
    wx.vibrateShort({ type: 'medium' });
    const app = getApp();
    const id = e.currentTarget.dataset.id;
    app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.performAddToCartAndView(id);
      }
    });
  },

  // 执行添加到购物车并查看操作
  performAddToCartAndView: function(id) {
    // 显示加载提示
    wx.showLoading({
      title: '正在添加到购物车',
      mask: true
    });

    // 使用API添加商品到购物车
    cartApi.addToCart(id, 1)
      .then(res => {
        wx.hideLoading();

        if (res.success) {
          // 更新购物车数量
          this.getCartCount();
          console.log('API添加购物车成功');

          // 显示成功提示
          wx.showToast({
            title: '已加入购物车',
            icon: 'success',
            duration: 500
          });

          // 延迟跳转，给用户一个反馈过程
          setTimeout(() => {
            console.log('准备跳转到购物车页面');

            // 跳转到购物车页面
            wx.navigateTo({
              url: '/pages/cart/cart',
              success: function() {
                console.log('成功跳转到购物车页面');
              },
              fail: function(error) {
                console.error('跳转到购物车页面失败:', error);

                // 尝试使用switchTab（如果购物车是Tab页面）
                wx.switchTab({
                  url: '/pages/cart/cart',
                  success: function() {
                    console.log('成功通过switchTab跳转到购物车页面');
                  },
                  fail: function(switchError) {
                    console.error('通过switchTab跳转到购物车页面失败:', switchError);
                    wx.showToast({
                      title: '跳转失败，请手动进入购物车',
                      icon: 'none'
                    });
                  }
                });
              }
            });
          }, 600);
        } else {
          wx.showToast({
            title: res.message || '添加失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('API添加购物车出错:', err);
        wx.hideLoading();

        // 显示错误提示
        wx.showToast({
          title: '网络错误，请稍后再试',
          icon: 'none'
        });
      });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.setData({
      refreshing: true,
      page: 1
    });

    this.getProducts();
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (!this.data.hasMore || this.data.loading) {
      return;
    }

    this.setData({
      page: this.data.page + 1,
      loading: true
    });

    this.getProducts(true);
  },

  // 获取轮播图数据
  getBanners: function() {
    console.log('开始获取轮播图数据');
    // 优先请求商品轮播图API
    productApi.getBanners()
      .then(res => {
        if (res.success && res.data && res.data.length > 0) {
          this.setData({ banners: res.data });
          console.log('已使用商品API轮播图数据:', res.data);
        } else {
          // 商品API无数据，尝试帖子API
          console.warn('商品API无数据，尝试帖子API');
          postApi.getBanners()
            .then(postRes => {
              if (postRes.success && postRes.data && postRes.data.length > 0) {
                this.setData({ banners: postRes.data });
                console.log('已使用帖子API轮播图数据:', postRes.data);
              }
            })
            .catch(postErr => {
              console.error('帖子API获取失败:', postErr);
            });
        }
      })
      .catch(err => {
        // 商品API失败，尝试帖子API
        console.error('商品API获取失败，尝试帖子API:', err);
        postApi.getBanners()
          .then(postRes => {
            if (postRes.success && postRes.data && postRes.data.length > 0) {
              this.setData({ banners: postRes.data });
              console.log('已使用帖子API轮播图数据:', postRes.data);
            }
          })
          .catch(postErr => {
            console.error('帖子API获取失败:', postErr);
          });
      });
  },

  // 点击轮播图
  onBannerTap: function(e) {
    const index = e.currentTarget.dataset.index;
    const banner = this.data.banners[index];

    console.log('点击轮播图:', banner);

    if (banner.type === 'url') {
      // 打开网页
      console.log('打开网页:', banner.url || banner.linkUrl);
      wx.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(banner.url || banner.linkUrl)}`
      });
    } else if (banner.type === 'page') {
      // 打开小程序页面
      console.log('打开小程序页面:', banner.url || banner.linkUrl);
      wx.navigateTo({
        url: banner.url || banner.linkUrl
      });
    } else {
      // 默认处理
      console.log('默认处理轮播图点击:', banner.linkUrl);
      if (banner.linkUrl) {
        wx.navigateTo({
          url: banner.linkUrl
        });
      }
    }
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索确认（键盘回车）
  onSearchConfirm: function() {
    const keyword = this.data.searchKeyword.trim();
    if (keyword) {
      wx.navigateTo({
        url: `/pages/search/search?keyword=${encodeURIComponent(keyword)}&type=product`
      });
    }
  },

  // 点击搜索按钮
  onSearchTap: function() {
    const keyword = this.data.searchKeyword.trim();
    if (keyword) {
      wx.navigateTo({
        url: `/pages/search/search?keyword=${encodeURIComponent(keyword)}&type=product`
      });
    } else {
      wx.navigateTo({
        url: '/pages/search/search?type=product'
      });
    }
  },

  // 获取购物车数量（不强制登录，仅用于显示）
  getCartCount: function() {
    // 使用API获取购物车数据
    cartApi.getCartItems()
      .then(res => {
        if (res.success) {
          const apiCartItems = res.data;
          let apiCount = 0;
          apiCartItems.forEach(item => {
            apiCount += (item.quantity || 1);
          });
          this.setData({ cartCount: apiCount });
        } else {
          this.setData({ cartCount: 0 });
        }
      })
      .catch(err => {
        // 不弹窗报错，静默失败
        this.setData({ cartCount: 0 });
      });
  },

  // 查看购物车（需要登录）
  viewCart: function() {
    wx.vibrateShort({ type: 'medium' });
    const app = getApp();
    app.checkNeedLogin((isLogin) => {
      if (isLogin) {
        this.performViewCart();
      }
    });
  },

  // 执行查看购物车操作
  performViewCart: function() {
    // 显示加载提示
    wx.showLoading({
      title: '正在打开购物车',
      mask: true
    });

    // 确保购物车页面存在
    const pages = getCurrentPages();
    const cartPageExists = pages.some(page => page.route === 'pages/cart/cart');

    console.log('当前页面栈:', pages.map(p => p.route));
    console.log('购物车页面是否存在:', cartPageExists);

    // 延迟跳转，给用户一个反馈过程
    setTimeout(() => {
      wx.hideLoading();
      console.log('准备跳转到购物车页面');
      // 跳转到购物车页面
      wx.navigateTo({
        url: '/pages/cart/cart',
        success: function() {
          console.log('成功跳转到购物车页面');
        },
        fail: function(error) {
          console.error('跳转到购物车页面失败:', error);
          // 尝试使用switchTab（如果购物车是Tab页面）
          wx.switchTab({
            url: '/pages/cart/cart',
            success: function() {
              console.log('成功通过switchTab跳转到购物车页面');
            },
            fail: function(switchError) {
              console.error('通过switchTab跳转到购物车页面失败:', switchError);
              wx.showToast({
                title: '跳转失败，请手动进入购物车',
                icon: 'none'
              });
            }
          });
        }
      });
    }, 800); // 增加延迟时间
  },

  // 更新购物车数量（从本地存储获取实际数量）
  updateCartCount: function() {
    // 从本地存储获取购物车数据
    const cartItems = wx.getStorageSync('cartItems') || [];

    // 计算购物车中商品总数量
    let count = 0;
    cartItems.forEach(item => {
      count += (item.quantity || 1);
    });

    // 更新数据
    this.setData({
      cartCount: count
    });

    console.log('购物车数量已更新:', count);
  },

  // 跳转到积分套餐分类
  goToPointsPackage: function() {
    // 设置平台使用分类（索引为1）
    this.setData({
      currentCategory: 1,
      loading: true,
      page: 1,
      products: []
    });

    // 获取平台使用的子分类
    this.getSubCategories(2, this.data.options);

    // 等待子分类加载完成后，切换到积分套餐子分类（索引为3）
    setTimeout(() => {
      this.setData({
        currentSubCategory: 3,
        loading: true,
        page: 1,
        products: []
      });

      // 获取商品
      this.getProducts();
    }, 500);
  },

  // 手动添加会员产品
  // addMemberProducts: function() {
  //   ...
  // },
});
