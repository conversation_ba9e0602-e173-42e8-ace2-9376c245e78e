const mysql = require('mysql2/promise');
const config = require('./config');

async function migrateDatabase() {
  try {
    const connection = await mysql.createConnection({
      host: config.db.host,
      user: config.db.user,
      password: config.db.password,
      database: config.db.database
    });

    // 检查表是否存在
    const [tables] = await connection.query('SHOW TABLES');
    const tableNames = tables.map(table => Object.values(table)[0]);

    // 如果表不存在，创建表
    if (!tableNames.includes('users')) {
      await connection.query(`
        CREATE TABLE users (
          id VARCHAR(20) PRIMARY KEY,
          username VARCHAR(50) NOT NULL,
          password VARCHAR(100) NOT NULL,
          nickname VARCHAR(50),
          avatar VARCHAR(255),
          gender ENUM('male', 'female', 'other'),
          phone VARCHAR(20),
          email VARCHAR(100),
          createTime BIGINT,
          updateTime BIGINT
        )
      `);
    }

    if (!tableNames.includes('posts')) {
      await connection.query(`
        CREATE TABLE posts (
          id VARCHAR(24) PRIMARY KEY,
          userId VARCHAR(20) NOT NULL,
          content TEXT,
          images TEXT,
          video VARCHAR(255),
          topic VARCHAR(100),
          location VARCHAR(255),
          isPublic BOOLEAN DEFAULT true,
          allowComment BOOLEAN DEFAULT true,
          allowForward BOOLEAN DEFAULT true,
          linkedProducts TEXT,
          region VARCHAR(100),
          userInfo TEXT,
          createTime BIGINT,
          likeCount INT DEFAULT 0,
          commentCount INT DEFAULT 0,
          shareCount INT DEFAULT 0,
          FOREIGN KEY (userId) REFERENCES users(id)
        )
      `);
    }

    if (!tableNames.includes('comments')) {
      await connection.query(`
        CREATE TABLE comments (
          id VARCHAR(24) PRIMARY KEY,
          postId VARCHAR(24) NOT NULL,
          userId VARCHAR(20) NOT NULL,
          content TEXT NOT NULL,
          createTime BIGINT,
          userNickname VARCHAR(100),
          userAvatar VARCHAR(255),
          FOREIGN KEY (postId) REFERENCES posts(id),
          FOREIGN KEY (userId) REFERENCES users(id)
        )
      `);
    } else {
      // 如果comments表已存在，检查并添加缺失的字段
      const [columns] = await connection.query('SHOW COLUMNS FROM comments');
      const columnNames = columns.map(col => col.Field);

      if (!columnNames.includes('userNickname')) {
        await connection.query('ALTER TABLE comments ADD COLUMN userNickname VARCHAR(100)');
        console.log('已添加userNickname字段到comments表');
      }

      if (!columnNames.includes('userAvatar')) {
        await connection.query('ALTER TABLE comments ADD COLUMN userAvatar VARCHAR(255)');
        console.log('已添加userAvatar字段到comments表');
      }
    }

    if (!tableNames.includes('likes')) {
      await connection.query(`
        CREATE TABLE likes (
          id VARCHAR(24) PRIMARY KEY,
          postId VARCHAR(24) NOT NULL,
          userId VARCHAR(20) NOT NULL,
          createTime BIGINT,
          FOREIGN KEY (postId) REFERENCES posts(id),
          FOREIGN KEY (userId) REFERENCES users(id)
        )
      `);
    }    // 如果users表已存在但没有referrerId字段，自动添加
    if (tableNames.includes('users')) {
      const [columns] = await connection.query('SHOW COLUMNS FROM users');
      const hasReferrer = columns.some(col => col.Field === 'referrerId');
      if (!hasReferrer) {
        await connection.query('ALTER TABLE users ADD COLUMN referrerId VARCHAR(20)');
      }
    }

    // 创建VIP会员等级表（如果不存在）
    if (!tableNames.includes('vip_levels')) {
      console.log('Creating vip_levels table...');
      await connection.query(`
        CREATE TABLE vip_levels (
          id INT AUTO_INCREMENT PRIMARY KEY,
          level_code VARCHAR(20) NOT NULL COMMENT '会员等级编码',
          level_name VARCHAR(50) NOT NULL COMMENT '会员等级名称',
          description TEXT COMMENT '会员等级描述',
          icon VARCHAR(255) COMMENT '等级图标URL',
          price DECIMAL(10, 2) NOT NULL DEFAULT 0 COMMENT '会员价格',
          duration INT NOT NULL DEFAULT 365 COMMENT '会员有效期(天)',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          UNIQUE KEY (level_code)
        )
      `);

      // 插入初始会员等级数据
      await connection.query(`
        INSERT INTO vip_levels (level_code, level_name, description, price, duration)
        VALUES
        ('free', '普通用户', '基础账户，可以浏览和使用基本功能', 0, 3650),
        ('annual', '年度会员', '标准会员，享受平台大部分功能和服务', 198, 365),
        ('super', '超级会员', '高级会员，享受更多权益和服务', 398, 365),
        ('v3', 'V3会员', '尊贵会员，享受全部平台权益和个性化服务', 998, 365)
      `);
    }

    // 创建会员权益表（如果不存在）
    if (!tableNames.includes('vip_benefits')) {
      console.log('Creating vip_benefits table...');
      await connection.query(`
        CREATE TABLE vip_benefits (
          id INT AUTO_INCREMENT PRIMARY KEY,
          level_code VARCHAR(20) NOT NULL COMMENT '会员等级编码',
          benefit_name VARCHAR(100) NOT NULL COMMENT '权益名称',
          benefit_desc TEXT COMMENT '权益描述',
          sort_order INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX (level_code)
        )
      `);

      // 插入初始会员权益数据
      await connection.query(`
        INSERT INTO vip_benefits (level_code, benefit_name, benefit_desc, sort_order)
        VALUES
        -- 年度会员权益
        ('annual', '专属会员标识与徽章', '在个人主页和评论中展示专属会员标识', 1),
        ('annual', '7×12小时专属客服', '优先获得客服响应和解决问题', 2),
        ('annual', '免费参与会员专属活动', '参加平台定期举办的线上专属活动', 3),
        ('annual', '专属文档模板使用权', '使用会员专属的法律和商业文档模板', 4),
        ('annual', '每月免费咨询1次', '每月可获得1次免费专业咨询服务', 5),

        -- 超级会员权益
        ('super', '全部年度会员权益', '包含年度会员的所有权益', 1),
        ('super', '专享85折服务优惠', '平台所有付费服务可享受85折优惠', 2),
        ('super', '高级会员专属礼包', '每季度获得一次会员专属礼包', 3),
        ('super', '优先参与线下活动', '优先参与平台组织的线下活动', 4),
        ('super', '每月免费咨询3次', '每月可获得3次免费专业咨询服务', 5),
        ('super', '专属高级模板库', '使用更高级、更全面的文档模板库', 6),

        -- V3会员权益
        ('v3', '超级会员全部权益', '包含超级会员的所有权益', 1),
        ('v3', '一对一专属服务顾问', '指定专属顾问提供一对一服务', 2),
        ('v3', 'V3专属高级定制服务', '根据个人需求提供定制化服务', 3),
        ('v3', '无限制法律咨询', '不限次数的法律咨询服务', 4),
        ('v3', '线下活动免费参与', '免费参与所有线下活动', 5),
        ('v3', '项目优先对接', '业务需求优先对接专业服务团队', 6)
      `);
    }

    // 创建用户会员表（如果不存在）
    if (!tableNames.includes('user_vip')) {
      console.log('Creating user_vip table...');
      await connection.query(`
        CREATE TABLE user_vip (
          id INT AUTO_INCREMENT PRIMARY KEY,
          user_id VARCHAR(20) NOT NULL COMMENT '用户ID',
          level_code VARCHAR(20) NOT NULL COMMENT '会员等级编码',
          start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
          expire_time TIMESTAMP NOT NULL COMMENT '过期时间',
          is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否有效',
          payment_id VARCHAR(50) COMMENT '支付ID',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          UNIQUE KEY (user_id),
          INDEX (level_code)
        )
      `);

      // 为现有用户添加默认会员数据
      await connection.query(`
        INSERT IGNORE INTO user_vip (user_id, level_code, start_time, expire_time)
        SELECT id, 'free', NOW(), DATE_ADD(NOW(), INTERVAL 10 YEAR) FROM users
      `);
    }

    // 创建公司信息表（如果不存在）
    if (!tableNames.includes('company_info')) {
      await connection.query(`
        CREATE TABLE company_info (
          id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
          company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
          company_description TEXT COMMENT '公司简介',
          company_address VARCHAR(255) COMMENT '公司地址',
          company_phone VARCHAR(50) COMMENT '公司电话',
          company_email VARCHAR(100) COMMENT '公司邮箱',
          company_website VARCHAR(255) COMMENT '公司网站',
          business_hours VARCHAR(100) COMMENT '营业时间',
          company_logo VARCHAR(255) COMMENT '公司Logo',
          contact_person VARCHAR(50) COMMENT '联系人',
          fax VARCHAR(50) COMMENT '传真号码',
          postal_code VARCHAR(20) COMMENT '邮政编码',
          company_type VARCHAR(50) COMMENT '公司类型',
          established_date DATE COMMENT '成立日期',
          registration_number VARCHAR(100) COMMENT '注册号码',
          social_credit_code VARCHAR(100) COMMENT '统一社会信用代码',
          legal_representative VARCHAR(50) COMMENT '法定代表人',
          registered_capital DECIMAL(15,2) COMMENT '注册资本',
          business_scope TEXT COMMENT '经营范围',
          company_status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '公司状态',
          createTime BIGINT NOT NULL COMMENT '创建时间',
          updateTime BIGINT NOT NULL COMMENT '更新时间',
          INDEX idx_company_name (company_name),
          INDEX idx_create_time (createTime),
          INDEX idx_update_time (updateTime)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公司信息表'
      `);
      console.log('公司信息表创建成功');
    }

    // 创建用户留言表（如果不存在）
    if (!tableNames.includes('user_feedback')) {
      await connection.query(`
        CREATE TABLE user_feedback (
          id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
          user_id VARCHAR(20) COMMENT '用户ID（可为空，支持未登录用户留言）',
          name VARCHAR(50) NOT NULL COMMENT '留言人姓名',
          contact VARCHAR(20) NOT NULL COMMENT '联系方式（手机号）',
          message TEXT NOT NULL COMMENT '留言内容',
          status ENUM('pending', 'processing', 'resolved') DEFAULT 'pending' COMMENT '处理状态',
          admin_reply TEXT COMMENT '管理员回复',
          create_time BIGINT NOT NULL COMMENT '创建时间',
          update_time BIGINT NOT NULL COMMENT '更新时间',
          INDEX idx_user_id (user_id),
          INDEX idx_status (status),
          INDEX idx_create_time (create_time),
          INDEX idx_contact (contact)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户留言表'
      `);
      console.log('用户留言表创建成功');
    }

    // 创建商品分类表（如果不存在）
    if (!tableNames.includes('categories')) {
      await connection.query(`
        CREATE TABLE categories (
          id INT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID',
          name VARCHAR(100) NOT NULL COMMENT '分类名称',
          description TEXT COMMENT '分类描述',
          icon VARCHAR(255) COMMENT '分类图标',
          sort INT DEFAULT 0 COMMENT '排序',
          createTime BIGINT NOT NULL COMMENT '创建时间',
          updateTime BIGINT NOT NULL COMMENT '更新时间',
          INDEX idx_sort (sort),
          INDEX idx_create_time (createTime)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表'
      `);
      console.log('商品分类表创建成功');

      // 插入初始分类数据
      const now = Date.now();
      await connection.query(`
        INSERT INTO categories (id, name, description, sort, createTime, updateTime)
        VALUES
        (1, '企业服务', '工商注册、财税服务、知识产权等企业服务', 1, ${now}, ${now}),
        (2, '平台服务', '会员服务、平台指南、增值服务等', 2, ${now}, ${now}),
        (3, '合作加盟', '城市合伙人、服务商、推广员等合作项目', 3, ${now}, ${now}),
        (4, '实物商品', '办公用品、数码产品、礼品定制等', 4, ${now}, ${now})
      `);
      console.log('商品分类初始数据插入成功');
    }

    // 创建商品子分类表（如果不存在）
    if (!tableNames.includes('subCategories')) {
      await connection.query(`
        CREATE TABLE subCategories (
          id INT AUTO_INCREMENT PRIMARY KEY COMMENT '子分类ID',
          parentId INT NOT NULL COMMENT '父分类ID',
          name VARCHAR(100) NOT NULL COMMENT '子分类名称',
          description TEXT COMMENT '子分类描述',
          icon VARCHAR(255) COMMENT '子分类图标',
          sort INT DEFAULT 0 COMMENT '排序',
          createTime BIGINT NOT NULL COMMENT '创建时间',
          updateTime BIGINT NOT NULL COMMENT '更新时间',
          INDEX idx_parent_id (parentId),
          INDEX idx_sort (sort),
          INDEX idx_create_time (createTime)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品子分类表'
      `);
      console.log('商品子分类表创建成功');

      // 插入初始子分类数据
      const now = Date.now();
      await connection.query(`
        INSERT INTO subCategories (id, parentId, name, description, sort, createTime, updateTime)
        VALUES
        -- 企业服务子分类
        (101, 1, '工商注册', '公司注册、变更、注销等工商服务', 1, ${now}, ${now}),
        (102, 1, '财税服务', '代理记账、税务申报、财务咨询等', 2, ${now}, ${now}),
        (103, 1, '知识产权', '商标注册、专利申请、版权保护等', 3, ${now}, ${now}),
        (104, 1, '法律服务', '合同审查、法律咨询、诉讼代理等', 4, ${now}, ${now}),
        -- 平台服务子分类
        (201, 2, '会员服务', '会员权益、会员升级等服务', 1, ${now}, ${now}),
        (202, 2, '平台指南', '使用教程、操作指南等', 2, ${now}, ${now}),
        (203, 2, '增值服务', '专属客服、优先处理等增值服务', 3, ${now}, ${now}),
        (204, 2, '积分套餐', '积分充值、积分兑换等', 4, ${now}, ${now}),
        -- 合作加盟子分类
        (301, 3, '城市合伙人', '城市合伙人招募和管理', 1, ${now}, ${now}),
        (302, 3, '服务商', '服务商入驻和合作', 2, ${now}, ${now}),
        (303, 3, '推广员', '推广员招募和奖励', 3, ${now}, ${now}),
        -- 实物商品子分类
        (401, 4, '办公用品', '文具、办公设备等', 1, ${now}, ${now}),
        (402, 4, '数码产品', '电脑、手机、配件等', 2, ${now}, ${now}),
        (403, 4, '礼品定制', '企业礼品、定制商品等', 3, ${now}, ${now})
      `);
      console.log('商品子分类初始数据插入成功');
    }

    // 创建商品表（如果不存在）
    if (!tableNames.includes('products')) {
      await connection.query(`
        CREATE TABLE products (
          id INT AUTO_INCREMENT PRIMARY KEY COMMENT '商品ID',
          name VARCHAR(255) NOT NULL COMMENT '商品名称',
          description TEXT COMMENT '商品描述',
          price DECIMAL(10, 2) NOT NULL DEFAULT 0 COMMENT '商品价格',
          originalPrice DECIMAL(10, 2) COMMENT '原价',
          images TEXT COMMENT '商品图片（JSON数组）',
          categoryId INT COMMENT '分类ID',
          subCategoryId INT COMMENT '子分类ID',
          stock INT DEFAULT 0 COMMENT '库存数量',
          salesCount INT DEFAULT 0 COMMENT '销售数量',
          status ENUM('active', 'inactive', 'deleted') DEFAULT 'active' COMMENT '商品状态',
          tags VARCHAR(500) COMMENT '商品标签',
          specifications TEXT COMMENT '商品规格（JSON）',
          createTime BIGINT NOT NULL COMMENT '创建时间',
          updateTime BIGINT NOT NULL COMMENT '更新时间',
          INDEX idx_category_id (categoryId),
          INDEX idx_sub_category_id (subCategoryId),
          INDEX idx_status (status),
          INDEX idx_price (price),
          INDEX idx_sales_count (salesCount),
          INDEX idx_create_time (createTime)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表'
      `);
      console.log('商品表创建成功');

      // 插入示例商品数据
      const now = Date.now();
      await connection.query(`
        INSERT INTO products (name, description, price, originalPrice, images, categoryId, subCategoryId, stock, salesCount, tags, createTime, updateTime)
        VALUES
        ('公司注册服务', '提供一站式公司注册服务，包含核名、工商登记、刻章等全流程服务', 499.00, 699.00, '["images/products/company-register.jpg"]', 1, 101, 999, 156, '工商注册,公司注册,创业', ${now}, ${now}),
        ('代理记账服务', '专业财务团队提供代理记账服务，月度报税、财务报表等', 299.00, 399.00, '["images/products/accounting.jpg"]', 1, 102, 999, 89, '代理记账,财税服务,报税', ${now}, ${now}),
        ('商标注册', '商标查询、申请、跟踪一站式服务，专业顾问全程指导', 599.00, 799.00, '["images/products/trademark.jpg"]', 1, 103, 999, 234, '商标注册,知识产权,品牌保护', ${now}, ${now}),
        ('年度会员', '享受平台所有基础服务，专属客服，优先处理', 198.00, 298.00, '["images/products/vip-annual.jpg"]', 2, 201, 999, 67, '会员服务,年费会员,优惠', ${now}, ${now}),
        ('超级会员', '享受平台所有服务85折优惠，专属礼包，线下活动优先', 398.00, 598.00, '["images/products/vip-super.jpg"]', 2, 201, 999, 23, '超级会员,VIP服务,折扣', ${now}, ${now})
      `);
      console.log('商品示例数据插入成功');
    }

    // 创建轮播图表（如果不存在）
    if (!tableNames.includes('banners')) {
      await connection.query(`
        CREATE TABLE banners (
          id INT AUTO_INCREMENT PRIMARY KEY COMMENT '轮播图ID',
          title VARCHAR(255) COMMENT '轮播图标题',
          imageUrl VARCHAR(500) NOT NULL COMMENT '图片URL',
          linkUrl VARCHAR(500) COMMENT '链接URL',
          type ENUM('page', 'product', 'external') DEFAULT 'page' COMMENT '链接类型',
          sort INT DEFAULT 0 COMMENT '排序',
          status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
          createTime BIGINT NOT NULL COMMENT '创建时间',
          updateTime BIGINT NOT NULL COMMENT '更新时间',
          INDEX idx_sort (sort),
          INDEX idx_status (status),
          INDEX idx_create_time (createTime)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='轮播图表'
      `);
      console.log('轮播图表创建成功');

      // 插入示例轮播图数据
      const now = Date.now();
      await connection.query(`
        INSERT INTO banners (title, imageUrl, linkUrl, type, sort, status, createTime, updateTime)
        VALUES
        ('企业服务专区', 'images/banners/enterprise-service.jpg', '/pages/shop/shop?category=1', 'page', 1, 'active', ${now}, ${now}),
        ('会员特惠活动', 'images/banners/vip-promotion.jpg', '/pages/vip/center', 'page', 2, 'active', ${now}, ${now}),
        ('新用户注册优惠', 'images/banners/new-user.jpg', '/pages/auth/auth', 'page', 3, 'active', ${now}, ${now})
      `);
      console.log('轮播图示例数据插入成功');
    }

    // 创建购物车表（如果不存在）
    if (!tableNames.includes('cart')) {
      await connection.query(`
        CREATE TABLE cart (
          id VARCHAR(24) PRIMARY KEY COMMENT '购物车项ID',
          userId VARCHAR(20) NOT NULL COMMENT '用户ID',
          productId INT NOT NULL COMMENT '商品ID',
          name VARCHAR(255) NOT NULL COMMENT '商品名称',
          price DECIMAL(10, 2) NOT NULL COMMENT '商品价格',
          image VARCHAR(500) COMMENT '商品图片',
          quantity INT NOT NULL DEFAULT 1 COMMENT '数量',
          selected BOOLEAN DEFAULT TRUE COMMENT '是否选中',
          createTime BIGINT NOT NULL COMMENT '创建时间',
          updateTime BIGINT NOT NULL COMMENT '更新时间',
          INDEX idx_user_id (userId),
          INDEX idx_product_id (productId),
          INDEX idx_create_time (createTime)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='购物车表'
      `);
      console.log('购物车表创建成功');
    }

    await connection.end();
    console.log('数据库迁移完成');
    return true;
  } catch (error) {
    console.error('数据库迁移失败:', error);
    throw error;
  }
}

module.exports = {
  migrateDatabase
};

// 如果直接运行此文件，执行迁移
if (require.main === module) {
  migrateDatabase().then(() => {
    console.log('迁移完成');
    process.exit(0);
  }).catch(error => {
    console.error('迁移失败:', error);
    process.exit(1);
  });
}